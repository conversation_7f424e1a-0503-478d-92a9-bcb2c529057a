#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 无头模式专用配置
针对无头模式下表情符号输入和发帖按钮激活问题的专门配置
"""

# 🤖 无头模式检测配置
HEADLESS_DETECTION_CONFIG = {
    # JavaScript检测特征
    'detection_features': [
        'navigator.webdriver === true',
        'window.outerHeight === 0',
        'window.outerWidth === 0', 
        'screen.width === 0 || screen.height === 0',
        '!window.chrome || !window.chrome.runtime',
        'navigator.plugins.length === 0'
    ],
    
    # 检测超时时间
    'detection_timeout': 5,
    
    # 是否启用详细检测日志
    'verbose_detection': True
}

# 🎭 无头模式输入策略配置
HEADLESS_INPUT_CONFIG = {
    # 输入方法优先级（按顺序尝试）
    'input_methods': [
        {
            'name': '增强JavaScript方法',
            'method': '_input_emoji_via_js_headless',
            'timeout': 10,
            'retry_count': 2
        },
        {
            'name': '无头剪贴板方法', 
            'method': '_input_via_clipboard_headless',
            'timeout': 8,
            'retry_count': 1
        },
        {
            'name': '直接DOM操作',
            'method': '_input_via_direct_dom',
            'timeout': 5,
            'retry_count': 2
        },
        {
            'name': '模拟键盘输入',
            'method': '_input_via_keyboard_simulation',
            'timeout': 15,
            'retry_count': 1
        }
    ],
    
    # 输入验证配置
    'validation': {
        'min_success_rate': 0.7,  # 至少70%的内容输入成功
        'wait_after_input': 0.5,  # 输入后等待时间
        'max_verification_attempts': 3
    },
    
    # 事件触发配置
    'events': {
        'trigger_all_events': True,  # 是否触发所有可能的事件
        'event_delay': 0.1,  # 事件间延迟
        'custom_events': True  # 是否触发自定义事件
    }
}

# 🔘 无头模式按钮检测配置
HEADLESS_BUTTON_CONFIG = {
    # 按钮状态检测配置
    'detection': {
        'max_wait_time': 45,  # 最大等待时间（秒）
        'check_interval': 0.5,  # 检查间隔（秒）
        'progress_report_interval': 5,  # 进度报告间隔（秒）
    },
    
    # 按钮状态检查项
    'status_checks': [
        'isEnabled',      # 基础启用状态
        'isVisible',      # 可见性
        'isDisplayed',    # 显示状态
        'ariaDisabled',   # aria-disabled属性
        'dataDisabled',   # data-disabled属性
        'opacity',        # 透明度
        'pointerEvents',  # 指针事件
        'className',      # CSS类名
        'buttonText'      # 按钮文本
    ],
    
    # 处理状态关键词
    'processing_keywords': [
        'uploading', 'processing', 'loading', 'disabled',
        '上传中', '处理中', '加载中', '禁用', 'wait', 'pending'
    ],
    
    # 禁用状态关键词
    'disabled_keywords': [
        'disabled', 'inactive', 'unavailable', 'blocked'
    ],
    
    # 按钮可用性阈值
    'thresholds': {
        'min_opacity': 0.5,  # 最小透明度
        'max_wait_timeout': 45  # 最大等待超时
    },
    
    # 超时后的处理策略
    'timeout_strategy': {
        'check_content': True,  # 检查是否有内容
        'force_enable': False,  # 是否强制启用
        'content_threshold': 1  # 内容长度阈值
    }
}

# 🎯 无头模式浏览器配置
HEADLESS_BROWSER_CONFIG = {
    # Chrome参数优化
    'chrome_args': [
        '--headless=new',
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-software-rasterizer',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--enable-features=NetworkService,NetworkServiceLogging',
        '--force-color-profile=srgb',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
    ],
    
    # 性能优化
    'performance': {
        'disable_images': True,
        'disable_css': False,  # 保留CSS以确保正确的元素状态
        'disable_javascript': False,  # 必须保留JavaScript
        'page_load_strategy': 'normal'  # 确保页面完全加载
    },
    
    # 超时配置
    'timeouts': {
        'page_load': 30,
        'script': 30,
        'implicit_wait': 10
    }
}

# 🔧 无头模式调试配置
HEADLESS_DEBUG_CONFIG = {
    # 是否启用调试模式
    'enabled': True,
    
    # 调试日志级别
    'log_level': 'DEBUG',
    
    # 是否保存调试截图
    'save_screenshots': True,
    'screenshot_path': 'debug_screenshots',
    
    # 是否保存页面源码
    'save_page_source': True,
    'page_source_path': 'debug_page_sources',
    
    # 是否记录详细的元素状态
    'log_element_states': True,
    
    # 是否记录JavaScript执行结果
    'log_js_results': True,
    
    # 调试信息保留时间（天）
    'retention_days': 7
}

# 🎪 无头模式特殊处理配置
HEADLESS_SPECIAL_CONFIG = {
    # 表情符号处理
    'emoji_handling': {
        'use_unicode_normalization': True,
        'fallback_to_text': False,
        'chunk_size': 10,  # 分块输入大小
        'max_emoji_length': 100  # 单次最大表情符号长度
    },
    
    # 文本处理
    'text_processing': {
        'normalize_whitespace': True,
        'remove_zero_width_chars': True,
        'handle_rtl_text': True
    },
    
    # 重试策略
    'retry_strategy': {
        'max_retries': 3,
        'retry_delay': 2,
        'exponential_backoff': True,
        'retry_on_timeout': True
    },
    
    # 错误处理
    'error_handling': {
        'continue_on_minor_errors': True,
        'log_all_errors': True,
        'fallback_to_basic_input': True
    }
}

# 🚀 获取完整的无头模式配置
def get_headless_config():
    """获取完整的无头模式配置"""
    return {
        'detection': HEADLESS_DETECTION_CONFIG,
        'input': HEADLESS_INPUT_CONFIG,
        'button': HEADLESS_BUTTON_CONFIG,
        'browser': HEADLESS_BROWSER_CONFIG,
        'debug': HEADLESS_DEBUG_CONFIG,
        'special': HEADLESS_SPECIAL_CONFIG
    }

# 🔧 配置验证函数
def validate_headless_config():
    """验证无头模式配置的有效性"""
    config = get_headless_config()
    
    # 基础验证
    assert config['button']['detection']['max_wait_time'] > 0
    assert config['button']['detection']['check_interval'] > 0
    assert 0 < config['input']['validation']['min_success_rate'] <= 1
    
    print("✅ 无头模式配置验证通过")
    return True

if __name__ == "__main__":
    # 验证配置
    validate_headless_config()
    
    # 打印配置摘要
    config = get_headless_config()
    print("🤖 无头模式配置摘要:")
    print(f"📊 输入方法数量: {len(config['input']['input_methods'])}")
    print(f"🔘 按钮检测项: {len(config['button']['status_checks'])}")
    print(f"🎯 Chrome参数: {len(config['browser']['chrome_args'])}")
    print(f"🔧 调试模式: {config['debug']['enabled']}")
